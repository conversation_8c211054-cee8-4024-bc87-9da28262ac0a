<?php

namespace App\Http\Controllers\API\V1\RatingReport;

use App\Http\Controllers\API\APIController;
use App\Models\LeadReport;
use App\Services\RatingReportService;
use App\Contracts\Repositories\ShopRepositoryInterface;
use App\Contracts\Repositories\DoctorRepositoryInterface;
use App\Models\Rating;
use Illuminate\Http\Request;

class RatingReportController extends APIController
{
    protected RatingReportService $ratingReportService;
    protected ShopRepositoryInterface $shopRepository;
    protected DoctorRepositoryInterface $doctorRepository;

    public function __construct(
        RatingReportService $ratingReportService,
        ShopRepositoryInterface $shopRepository,
        DoctorRepositoryInterface $doctorRepository
    ) {
        $this->ratingReportService = $ratingReportService;
        $this->shopRepository = $shopRepository;
        $this->doctorRepository = $doctorRepository;
    }

    /**
     * Get filters data for rating reports
     */
    public function getFilters()
    {
        // Check permissions - allow both viewReportDoctorAll and viewReportService
        if (!auth()->user()->can('viewReportDoctorAll', LeadReport::class) &&
            !auth()->user()->can('viewReportService', LeadReport::class)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $shops = $this->shopRepository->all()->map(function ($shop) {
            return [
                'id' => $shop->id,
                'name' => $shop->name
            ];
        });

        $doctors = $this->doctorRepository->all()->map(function ($doctor) {
            return [
                'id' => $doctor->id,
                'name' => $doctor->name
            ];
        });

        $serviceOptions = Rating::getServiceOptions();

        // Generate month options
        $months = [];
        for ($m = 1; $m <= 12; $m++) {
            $monthValue = str_pad($m, 2, '0', STR_PAD_LEFT);
            $months[] = [
                'value' => $monthValue,
                'label' => 'Tháng ' . $m
            ];
        }

        // Generate year options
        $currentYear = date('Y');
        $startYear = $currentYear - 2;
        $endYear = $currentYear + 2;
        $years = [];
        for ($y = $startYear; $y <= $endYear; $y++) {
            $years[] = [
                'value' => $y,
                'label' => $y
            ];
        }

        return $this->responseJsonSuccess([
            'shops' => $shops,
            'doctors' => $doctors,
            'service_options' => $serviceOptions,
            'months' => $months,
            'years' => $years
        ]);
    }

    /**
     * Get rating report matrix data
     */
    public function getMatrix(Request $request)
    {
        // Check permissions - allow both viewReportDoctorAll and viewReportService
        if (!auth()->user()->can('viewReportDoctorAll', LeadReport::class) &&
            !auth()->user()->can('viewReportService', LeadReport::class)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Validate request
        $request->validate([
            'shop_id' => 'nullable|exists:shops,id',
            'month_only' => 'nullable|string|size:2',
            'year' => 'nullable|integer|min:2020|max:2030',
            'min_rating' => 'nullable|numeric|min:0|max:10'
        ]);

        // Build filters
        $filters = [];

        if ($request->filled('shop_id')) {
            $filters['shop_id'] = $request->input('shop_id');
        }

        // Combine month and year into month filter
        if ($request->filled('month_only') && $request->filled('year')) {
            $filters['month'] = $request->input('year') . '-' . $request->input('month_only');
        } elseif ($request->filled('year')) {
            $filters['month'] = $request->input('year') . '-01'; // Default to January if only year provided
        }

        if ($request->filled('min_rating')) {
            $filters['min_rating'] = $request->input('min_rating');
        }

        // Get report data
        $reportData = $this->ratingReportService->getDoctorServiceMatrix($filters);

        // Transform data to LeadReport format with headers and rows
        $response = [
            'headers' => $this->getRatingReportHeaders($reportData['services']),
            'rows' => $this->transformMatrixToRows($reportData['doctors'], $reportData['services'], $reportData['matrix']),
            'filters_applied' => $filters
        ];

        return $this->responseJsonSuccess($response);
    }

    /**
     * Get headers for rating report (similar to LeadReport format)
     */
    private function getRatingReportHeaders(array $services): array
    {
        $headers = [
            [
                'key' => 'index',
                'label' => 'STT'
            ],
            [
                'key' => 'doctor_name',
                'label' => 'Bác sĩ'
            ]
        ];

        // Add service columns
        foreach ($services as $serviceKey => $serviceName) {
            $headers[] = [
                'key' => $this->convertToEnglishKey($serviceName),
                'label' => $serviceName
            ];
        }

        return $headers;
    }

    /**
     * Transform matrix data to rows format (similar to LeadReport format)
     */
    private function transformMatrixToRows($doctors, array $services, array $matrix): array
    {
        $rows = [];

        foreach ($doctors as $doctor) {
            $row = [
                'index' => $doctor->id, // Use doctor ID as index
                'doctor_name' => $doctor->name
            ];

            // Add rating data for each service
            foreach ($services as $serviceKey => $serviceName) {
                $ratingData = $matrix[$doctor->id][$serviceKey] ?? null;
                $englishKey = $this->convertToEnglishKey($serviceName);

                if ($ratingData && $ratingData['average'] !== null) {
                    $row[$englishKey] = [
                        'rating' => (float) number_format($ratingData['average'], 1),
                        'count' => $ratingData['count']
                    ];
                } else {
                    $row[$englishKey] = [
                        'rating' => null,
                        'count' => 0
                    ];
                }
            }

            $rows[] = $row;
        }

        return $rows;
    }

    /**
     * Convert Vietnamese service name to English key
     */
    private function convertToEnglishKey(string $serviceName): string
    {
        // Convert Vietnamese characters to English
        $vietnameseChars = [
            'à', 'á', 'ạ', 'ả', 'ã', 'â', 'ầ', 'ấ', 'ậ', 'ẩ', 'ẫ', 'ă', 'ằ', 'ắ', 'ặ', 'ẳ', 'ẵ',
            'è', 'é', 'ẹ', 'ẻ', 'ẽ', 'ê', 'ề', 'ế', 'ệ', 'ể', 'ễ',
            'ì', 'í', 'ị', 'ỉ', 'ĩ',
            'ò', 'ó', 'ọ', 'ỏ', 'õ', 'ô', 'ồ', 'ố', 'ộ', 'ổ', 'ỗ', 'ơ', 'ờ', 'ớ', 'ợ', 'ở', 'ỡ',
            'ù', 'ú', 'ụ', 'ủ', 'ũ', 'ư', 'ừ', 'ứ', 'ự', 'ử', 'ữ',
            'ỳ', 'ý', 'ỵ', 'ỷ', 'ỹ',
            'đ',
            'À', 'Á', 'Ạ', 'Ả', 'Ã', 'Â', 'Ầ', 'Ấ', 'Ậ', 'Ẩ', 'Ẫ', 'Ă', 'Ằ', 'Ắ', 'Ặ', 'Ẳ', 'Ẵ',
            'È', 'É', 'Ẹ', 'Ẻ', 'Ẽ', 'Ê', 'Ề', 'Ế', 'Ệ', 'Ể', 'Ễ',
            'Ì', 'Í', 'Ị', 'Ỉ', 'Ĩ',
            'Ò', 'Ó', 'Ọ', 'Ỏ', 'Õ', 'Ô', 'Ồ', 'Ố', 'Ộ', 'Ổ', 'Ỗ', 'Ơ', 'Ờ', 'Ớ', 'Ợ', 'Ở', 'Ỡ',
            'Ù', 'Ú', 'Ụ', 'Ủ', 'Ũ', 'Ư', 'Ừ', 'Ứ', 'Ự', 'Ử', 'Ữ',
            'Ỳ', 'Ý', 'Ỵ', 'Ỷ', 'Ỹ',
            'Đ'
        ];

        $englishChars = [
            'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a',
            'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e',
            'i', 'i', 'i', 'i', 'i',
            'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o',
            'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u',
            'y', 'y', 'y', 'y', 'y',
            'd',
            'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a',
            'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e',
            'i', 'i', 'i', 'i', 'i',
            'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o',
            'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u',
            'y', 'y', 'y', 'y', 'y',
            'd'
        ];

        // Replace Vietnamese characters
        $converted = str_replace($vietnameseChars, $englishChars, $serviceName);

        // Convert to lowercase and replace spaces/special chars with underscore
        $converted = strtolower($converted);
        $converted = preg_replace('/[^a-z0-9]+/', '_', $converted);
        $converted = trim($converted, '_');

        return $converted;
    }

    /**
     * Get CSS class based on rating value
     */
    private function getRatingClass($average): ?string
    {
        if ($average === null) {
            return null;
        }

        if ($average >= 8) {
            return 'text-success';
        } elseif ($average >= 6) {
            return 'text-warning';
        } else {
            return 'text-danger';
        }
    }
}
