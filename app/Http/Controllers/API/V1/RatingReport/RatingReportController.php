<?php

namespace App\Http\Controllers\API\V1\RatingReport;

use App\Http\Controllers\API\APIController;
use App\Models\LeadReport;
use App\Services\RatingReportService;
use App\Contracts\Repositories\ShopRepositoryInterface;
use App\Contracts\Repositories\DoctorRepositoryInterface;
use App\Models\Rating;
use Illuminate\Http\Request;

class RatingReportController extends APIController
{
    protected RatingReportService $ratingReportService;
    protected ShopRepositoryInterface $shopRepository;
    protected DoctorRepositoryInterface $doctorRepository;

    public function __construct(
        RatingReportService $ratingReportService,
        ShopRepositoryInterface $shopRepository,
        DoctorRepositoryInterface $doctorRepository
    ) {
        $this->ratingReportService = $ratingReportService;
        $this->shopRepository = $shopRepository;
        $this->doctorRepository = $doctorRepository;
    }

    /**
     * Get filters data for rating reports
     */
    public function getFilters()
    {
        // Check permissions - allow both viewReportDoctorAll and viewReportService
        if (!auth()->user()->can('viewReportDoctorAll', LeadReport::class) &&
            !auth()->user()->can('viewReportService', LeadReport::class)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        $shops = $this->shopRepository->all()->map(function ($shop) {
            return [
                'id' => $shop->id,
                'name' => $shop->name
            ];
        });

        $doctors = $this->doctorRepository->all()->map(function ($doctor) {
            return [
                'id' => $doctor->id,
                'name' => $doctor->name
            ];
        });

        $serviceOptions = Rating::getServiceOptions();

        // Generate month options
        $months = [];
        for ($m = 1; $m <= 12; $m++) {
            $monthValue = str_pad($m, 2, '0', STR_PAD_LEFT);
            $months[] = [
                'value' => $monthValue,
                'label' => 'Tháng ' . $m
            ];
        }

        // Generate year options
        $currentYear = date('Y');
        $startYear = $currentYear - 2;
        $endYear = $currentYear + 2;
        $years = [];
        for ($y = $startYear; $y <= $endYear; $y++) {
            $years[] = [
                'value' => $y,
                'label' => $y
            ];
        }

        return $this->responseJsonSuccess([
            'shops' => $shops,
            'doctors' => $doctors,
            'service_options' => $serviceOptions,
            'months' => $months,
            'years' => $years
        ]);
    }

    /**
     * Get rating report matrix data
     */
    public function getMatrix(Request $request)
    {
        // Check permissions - allow both viewReportDoctorAll and viewReportService
        if (!auth()->user()->can('viewReportDoctorAll', LeadReport::class) &&
            !auth()->user()->can('viewReportService', LeadReport::class)) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 403);
        }

        // Validate request
        $request->validate([
            'shop_id' => 'nullable|exists:shops,id',
            'month_only' => 'nullable|string|size:2',
            'year' => 'nullable|integer|min:2020|max:2030',
            'min_rating' => 'nullable|numeric|min:0|max:10'
        ]);

        // Build filters
        $filters = [];

        if ($request->filled('shop_id')) {
            $filters['shop_id'] = $request->input('shop_id');
        }

        // Combine month and year into month filter
        if ($request->filled('month_only') && $request->filled('year')) {
            $filters['month'] = $request->input('year') . '-' . $request->input('month_only');
        } elseif ($request->filled('year')) {
            $filters['month'] = $request->input('year') . '-01'; // Default to January if only year provided
        }

        if ($request->filled('min_rating')) {
            $filters['min_rating'] = $request->input('min_rating');
        }

        // Get report data
        $reportData = $this->ratingReportService->getDoctorServiceMatrix($filters);

        // Transform data to LeadReport format with headers and rows
        $response = [
            'headers' => $this->getRatingReportHeaders($reportData['services']),
            'rows' => $this->transformMatrixToRows($reportData['doctors'], $reportData['services'], $reportData['matrix']),
            'filters_applied' => $filters
        ];

        return $this->responseJsonSuccess($response);
    }

    /**
     * Get headers for rating report (similar to LeadReport format)
     */
    private function getRatingReportHeaders(array $services): array
    {
        $headers = [
            'STT',
            'Bác sĩ'
        ];

        // Add service columns
        foreach ($services as $serviceKey => $serviceName) {
            $headers[] = $serviceName;
        }

        return $headers;
    }

    /**
     * Transform matrix data to rows format (similar to LeadReport format)
     */
    private function transformMatrixToRows($doctors, array $services, array $matrix): array
    {
        $rows = [];
        $index = 1;

        foreach ($doctors as $doctor) {
            $row = [
                $index++, // STT
                $doctor->name // Tên bác sĩ
            ];

            // Add rating data for each service
            foreach ($services as $serviceKey => $serviceName) {
                $ratingData = $matrix[$doctor->id][$serviceKey] ?? null;

                if ($ratingData && $ratingData['average'] !== null) {
                    $row[] = [
                        'value' => number_format($ratingData['average'], 1),
                        'count' => $ratingData['count'],
                        'min' => $ratingData['min'],
                        'max' => $ratingData['max'],
                        'rating_class' => $this->getRatingClass($ratingData['average']),
                        'formatted' => number_format($ratingData['average'], 1) . '/10 (' . $ratingData['count'] . ' đánh giá)'
                    ];
                } else {
                    $row[] = [
                        'value' => '--',
                        'count' => 0,
                        'min' => null,
                        'max' => null,
                        'rating_class' => null,
                        'formatted' => '--'
                    ];
                }
            }

            $rows[] = $row;
        }

        return $rows;
    }

    /**
     * Get CSS class based on rating value
     */
    private function getRatingClass($average): ?string
    {
        if ($average === null) {
            return null;
        }

        if ($average >= 8) {
            return 'text-success';
        } elseif ($average >= 6) {
            return 'text-warning';
        } else {
            return 'text-danger';
        }
    }
}
